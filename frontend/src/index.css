@import "tailwindcss";

/* Light theme (default) */
:root {
  --color-background: 0 0% 100%;
  --color-foreground: 222.2 84% 4.9%;
  --color-card: 0 0% 100%;
  --color-card-foreground: 222.2 84% 4.9%;
  --color-popover: 0 0% 100%;
  --color-popover-foreground: 222.2 84% 4.9%;
  --color-primary: 221.2 83.2% 53.3%;
  --color-primary-foreground: 210 40% 98%;
  --color-secondary: 210 40% 96%;
  --color-secondary-foreground: 222.2 84% 4.9%;
  --color-muted: 210 40% 96%;
  --color-muted-foreground: 215.4 16.3% 46.9%;
  --color-accent: 210 40% 96%;
  --color-accent-foreground: 222.2 84% 4.9%;
  --color-destructive: 0 84.2% 60.2%;
  --color-destructive-foreground: 210 40% 98%;
  --color-border: 214.3 31.8% 91.4%;
  --color-input: 214.3 31.8% 91.4%;
  --color-ring: 221.2 83.2% 53.3%;
  --radius: 0.75rem;
}

/* Dark theme */
.dark {
  --color-background: 222.2 84% 4.9%;
  --color-foreground: 210 40% 98%;
  --color-card: 222.2 84% 4.9%;
  --color-card-foreground: 210 40% 98%;
  --color-popover: 222.2 84% 4.9%;
  --color-popover-foreground: 210 40% 98%;
  --color-primary: 217.2 91.2% 59.8%;
  --color-primary-foreground: 222.2 84% 4.9%;
  --color-secondary: 217.2 32.6% 17.5%;
  --color-secondary-foreground: 210 40% 98%;
  --color-muted: 217.2 32.6% 17.5%;
  --color-muted-foreground: 215 20.2% 65.1%;
  --color-accent: 217.2 32.6% 17.5%;
  --color-accent-foreground: 210 40% 98%;
  --color-destructive: 0 62.8% 30.6%;
  --color-destructive-foreground: 210 40% 98%;
  --color-border: 217.2 32.6% 17.5%;
  --color-input: 217.2 32.6% 17.5%;
  --color-ring: 224.3 76.3% 94.1%;
}

/* System preference fallback */
@media (prefers-color-scheme: dark) {
  :root:not(.light) {
    --color-background: 222.2 84% 4.9%;
    --color-foreground: 210 40% 98%;
    --color-card: 222.2 84% 4.9%;
    --color-card-foreground: 210 40% 98%;
    --color-popover: 222.2 84% 4.9%;
    --color-popover-foreground: 210 40% 98%;
    --color-primary: 217.2 91.2% 59.8%;
    --color-primary-foreground: 222.2 84% 4.9%;
    --color-secondary: 217.2 32.6% 17.5%;
    --color-secondary-foreground: 210 40% 98%;
    --color-muted: 217.2 32.6% 17.5%;
    --color-muted-foreground: 215 20.2% 65.1%;
    --color-accent: 217.2 32.6% 17.5%;
    --color-accent-foreground: 210 40% 98%;
    --color-destructive: 0 62.8% 30.6%;
    --color-destructive-foreground: 210 40% 98%;
    --color-border: 217.2 32.6% 17.5%;
    --color-input: 217.2 32.6% 17.5%;
    --color-ring: 224.3 76.3% 94.1%;
  }
}

* {
  border-color: hsl(var(--color-border));
}

body {
  background-color: hsl(var(--color-background));
  color: hsl(var(--color-foreground));
  font-feature-settings: "rlig" 1, "calt" 1;
  margin: 0;
  min-height: 100vh;
  /* Mobile-specific optimizations */
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-size-adjust: 100%; /* Prevent text scaling on iOS */
  -webkit-text-size-adjust: 100%;
  -webkit-tap-highlight-color: transparent; /* Remove tap highlight on iOS */
  touch-action: manipulation; /* Improve touch responsiveness */
}

.animate-in {
  animation: fadeIn 0.5s ease-in-out;
}

.animate-slide-up {
  animation: slideUp 0.3s ease-out;
}

@keyframes fadeIn {
  0% { opacity: 0; }
  100% { opacity: 1; }
}

@keyframes slideUp {
  0% { transform: translateY(10px); opacity: 0; }
  100% { transform: translateY(0); opacity: 1; }
}

/* Line clamp utilities */
.line-clamp-1 {
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 1;
}

.line-clamp-2 {
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
}

.line-clamp-3 {
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 3;
}

/* Mobile-specific utilities */
.touch-manipulation {
  touch-action: manipulation;
}

.safe-area-inset {
  padding-top: env(safe-area-inset-top);
  padding-bottom: env(safe-area-inset-bottom);
  padding-left: env(safe-area-inset-left);
  padding-right: env(safe-area-inset-right);
}

.safe-area-inset-top {
  padding-top: env(safe-area-inset-top);
}

.safe-area-inset-bottom {
  padding-bottom: env(safe-area-inset-bottom);
}

/* Mobile-optimized scrolling */
.mobile-scroll {
  -webkit-overflow-scrolling: touch;
  overscroll-behavior: contain;
}

/* Touch-friendly focus states */
.touch-focus:focus-visible {
  outline: 2px solid hsl(var(--color-primary));
  outline-offset: 2px;
}

/* Mobile-first responsive text */
.text-responsive {
  font-size: 1rem;
  line-height: 1.5;
}

@media (min-width: 640px) {
  .text-responsive {
    font-size: 0.875rem;
    line-height: 1.4;
  }
}

/* Progress bar striped patterns */
.bg-stripes {
  background-image: linear-gradient(
    45deg,
    rgba(255, 255, 255, 0.15) 25%,
    transparent 25%,
    transparent 50%,
    rgba(255, 255, 255, 0.15) 50%,
    rgba(255, 255, 255, 0.15) 75%,
    transparent 75%,
    transparent
  );
  background-size: 20px 20px;
}

.bg-stripes-animated {
  background-image: linear-gradient(
    45deg,
    rgba(255, 255, 255, 0.2) 25%,
    transparent 25%,
    transparent 50%,
    rgba(255, 255, 255, 0.2) 50%,
    rgba(255, 255, 255, 0.2) 75%,
    transparent 75%,
    transparent
  );
  background-size: 20px 20px;
}

/* Performance optimizations for animations */
.will-change-transform {
  will-change: transform;
}

.will-change-opacity {
  will-change: opacity;
}

.will-change-contents {
  will-change: contents;
}

/* GPU acceleration for smooth animations */
.gpu-accelerated {
  transform: translateZ(0);
  backface-visibility: hidden;
  perspective: 1000px;
}

/* Smooth scrolling with momentum on iOS */
.smooth-scroll {
  -webkit-overflow-scrolling: touch;
  scroll-behavior: smooth;
}

/* Enhanced focus styles for accessibility */
.focus-visible-enhanced:focus-visible {
  outline: 2px solid hsl(var(--ring));
  outline-offset: 2px;
  box-shadow: 0 0 0 2px hsl(var(--background)), 0 0 0 4px hsl(var(--ring));
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .button {
    border: 2px solid currentColor;
  }

  .card {
    border: 2px solid currentColor;
  }
}

/* Reduced motion preferences */
@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
    scroll-behavior: auto !important;
  }

  /* Disable custom animations for reduced motion */
  .bg-stripes-animated {
    animation: none !important;
  }

  /* Disable transform animations but keep opacity for accessibility */
  [data-motion="reduce"] {
    transform: none !important;
    animation: none !important;
  }

  /* Keep essential transitions for usability */
  button, input, textarea, select {
    transition: background-color 0.2s ease, border-color 0.2s ease, color 0.2s ease !important;
  }
}

/* Mobile-specific layout utilities */
.mobile-container {
  width: 100%;
  max-width: 100%;
  padding-left: 1rem;
  padding-right: 1rem;
}

@media (min-width: 640px) {
  .mobile-container {
    padding-left: 1.5rem;
    padding-right: 1.5rem;
  }
}

@media (min-width: 1024px) {
  .mobile-container {
    max-width: 1200px;
    margin-left: auto;
    margin-right: auto;
  }
}

/* Touch-friendly interactive elements */
.touch-target {
  min-height: 44px;
  min-width: 44px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.touch-target-sm {
  min-height: 40px;
  min-width: 40px;
}

.touch-target-lg {
  min-height: 48px;
  min-width: 48px;
}

/* Mobile-optimized form elements */
input[type="text"],
input[type="email"],
input[type="password"],
input[type="search"],
textarea,
select {
  font-size: 16px; /* Prevent zoom on iOS */
}

@media (min-width: 640px) {
  input[type="text"],
  input[type="email"],
  input[type="password"],
  input[type="search"],
  textarea,
  select {
    font-size: 14px;
  }
}

/* Improved scrollbar styling */
.scrollbar-thin {
  scrollbar-width: thin;
  scrollbar-color: hsl(var(--color-border)) transparent;
}

.scrollbar-thin::-webkit-scrollbar {
  width: 6px;
}

.scrollbar-thin::-webkit-scrollbar-track {
  background: transparent;
}

.scrollbar-thin::-webkit-scrollbar-thumb {
  background-color: hsl(var(--color-border));
  border-radius: 3px;
}

.scrollbar-thin::-webkit-scrollbar-thumb:hover {
  background-color: hsl(var(--color-muted-foreground));
}

/* ContentEditable placeholder support */
.empty-editor:empty::before {
  content: attr(data-placeholder);
  color: hsl(var(--color-muted-foreground));
  pointer-events: none;
  position: absolute;
  opacity: 0.7;
}

.empty-editor:focus:empty::before {
  opacity: 0.5;
}

/* Toolbar visibility utilities */
.toolbar-always-visible {
  position: sticky !important;
  bottom: 0 !important;
  z-index: 10 !important;
}

/* Small screen optimization for editor */
@media (max-height: 600px) {
  .editor-container {
    max-height: calc(100vh - 200px) !important;
  }
  
  .toolbar-compact {
    min-height: 50px !important;
    padding: 0.5rem !important;
  }
  
  .toolbar-compact button {
    height: 1.5rem !important;
    padding: 0.25rem 0.5rem !important;
    font-size: 0.6rem !important;
  }
}

@media (max-height: 500px) {
  .editor-container {
    max-height: calc(100vh - 160px) !important;
  }
}
