# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db
*.tmp
*.temp

# IDE and Editor files
.vscode/*
!.vscode/extensions.json
!.vscode/settings.json
!.vscode/tasks.json
!.vscode/launch.json
.idea/
*.swp
*.swo
*~
.project
.metadata
.classpath
.settings/
*.sublime-project
*.sublime-workspace

# Node.js / Frontend
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*
lerna-debug.log*
.npm
.yarn-integrity
.pnpm-debug.log

# Build outputs
dist/
dist-ssr/
build/
out/
*.local

# Environment variables
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# Logs
logs/
*.log

# Runtime data
pids/
*.pid
*.seed
*.pid.lock

# Coverage directory used by tools like istanbul
coverage/
*.lcov

# Dependency directories
jspm_packages/

# Optional npm cache directory
.npm

# Optional eslint cache
.eslintcache

# Rust / Tauri
target/
Cargo.lock
**/*.rs.bk
*.pdb

# Tauri specific
src-tauri/target/
src-tauri/gen/
tauri-app/src-tauri/target/
tauri-app/src-tauri/gen/

# Application specific
*.exe
*.dmg
*.app
*.deb
*.rpm
*.msi

# Text recognition / ML models (if any)
*.model
*.weights
*.h5
*.pb
*.onnx
*.tflite

# Cache directories
.cache/
.parcel-cache/
.next/
.nuxt/
.vuepress/dist/

# Temporary folders
tmp/
temp/

# Lock files
package-lock.json
yarn.lock
pnpm-lock.yaml
bun.lock

# Test coverage
coverage/
.nyc_output/

# Storybook build outputs
storybook-static/

# Optional REPL history
.node_repl_history

# Output of 'npm pack'
*.tgz

# Yarn Integrity file
.yarn-integrity

# dotenv environment variables file
.env.test

# Stores VSCode versions used for testing VSCode extensions
.vscode-test

# Backup files
*.bak
*.backup
*.old

# System files
*.swp
*.swo
.fuse_hidden*

# JetBrains IDEs
.idea/
*.iml
*.ipr
*.iws

# Vim
*.swp
*.swo
*~

# Emacs
*~
\#*\#
/.emacs.desktop
/.emacs.desktop.lock
*.elc
auto-save-list
tramp
.\#*

# Windows
Thumbs.db
ehthumbs.db
Desktop.ini
$RECYCLE.BIN/
*.cab
*.msi
*.msm
*.msp
*.lnk
